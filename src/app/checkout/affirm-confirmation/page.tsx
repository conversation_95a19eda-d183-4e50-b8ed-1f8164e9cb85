import { cookies, headers } from 'next/headers';
import { redirect } from 'next/navigation';

import AffirmConfirmationContainer from '~/components/pages/CheckoutPage/AffirmConfirmation/AffirmConfirmation';
import { AppRouteCheckoutAffirmConfirmationPageParams } from '~/data/AppRoutePageParams';
import { SiteCartBillingResponse } from '~/data/models/SiteCartBillingResponse';
import { SiteCartShippingResponse } from '~/data/models/SiteCartShippingResponse';
import { SiteCartSummary } from '~/data/models/SiteCartSummary';
import { backendBootstrap } from '~/lib/backend/bootstrap';
import { backendGetCartBilling } from '~/lib/backend/checkout/cart-billing';
import { backendGetCartShipping } from '~/lib/backend/checkout/cart-shipping';
import { backendGetCartSummary } from '~/lib/backend/checkout/cart-summary';
import { COOKIES } from '~/lib/constants/cookies';
import { generateExtraQueryParams } from '~/lib/fetch-backend/extra-query-generator';
import { getUserIpForAppRoute } from '~/lib/utils/ip';
import { getStringifiedParams } from '~/lib/utils/routes';

interface Props {
  cartSummary: SiteCartSummary | null;
  checkout_token: string;
  siteBilling: SiteCartBillingResponse | null;
  siteShipping: SiteCartShippingResponse | null;
  userIp: string | null;
}

async function CheckoutAffirmConfirmationPage(
  appRoutePageParams: AppRouteCheckoutAffirmConfirmationPageParams,
) {
  await backendBootstrap();

  const pageParams = await appRoutePageParams.searchParams;
  const queryParams = getStringifiedParams(pageParams);
  const { checkout_token } = queryParams;

  const [cookieStore, headersList] = await Promise.all([cookies(), headers()]);
  const userIp = getUserIpForAppRoute(headersList);
  const storedCartId = cookieStore.get(COOKIES.CART_ID)?.value ?? null;

  if (!storedCartId) {
    redirect('/checkout/payments');
  }

  const extraQueryParams = await generateExtraQueryParams({
    includeUserRegion: true,
    includeUserSSOUid: true,
    includeUserZip: true,
    query: queryParams,
  });

  const [cartShippingResponse, cartBillingResponse, cartSummaryResponse] =
    await Promise.all([
      backendGetCartShipping({ cartId: storedCartId }, extraQueryParams),
      backendGetCartBilling({ cartId: storedCartId }, extraQueryParams),
      backendGetCartSummary(
        {
          id: storedCartId,
        },
        extraQueryParams,
      ),
    ]);

  const props: Props = {
    cartSummary: cartSummaryResponse.isSuccess
      ? cartSummaryResponse.data.siteCart
      : null,
    checkout_token,
    siteBilling: cartBillingResponse.isSuccess
      ? cartBillingResponse.data.siteCartBillingResponse
      : null,
    siteShipping: cartShippingResponse.isSuccess
      ? cartShippingResponse.data.siteCartShippingResponse
      : null,
    userIp: userIp || null,
  };

  return <AffirmConfirmationContainer {...props} />;
}

export default CheckoutAffirmConfirmationPage;
