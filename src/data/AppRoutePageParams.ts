type Params<U> = Promise<U>;
type SearchParams = Promise<{ [key: string]: string | string[] | undefined }>;

export interface AppRoutePageParams {
  params: Params<{ slug: string }>;
  searchParams: SearchParams;
}

export interface AppRouteTireShopPageParams {
  params: Params<{ shopCode: string; shopName: string }>;
  searchParams: SearchParams;
}

export interface AppRouteUrbanAreaPageParams {
  params: Params<{ urbanArea: string }>;
  searchParams: SearchParams;
}

export interface AppRoutePressCategoryPageParams {
  params: Params<{ category: string }>;
  searchParams: Params<{ page: string }>;
}

export interface AppRouteLearnCategoryPageParams {
  params: Params<{ category: string }>;
  searchParams: Params<{ page: string }>;
}

export interface AppRoutePressSlugPageParams {
  params: Params<{ category: string; slug: string }>;
  searchParams: SearchParams;
}

export interface AppRouteLearnSlugPageParams {
  params: Params<{ category: string; slug: string }>;
  searchParams: SearchParams;
}

export interface AppRouteTireShopPageParams {
  params: Params<{ shopCode: string; shopName: string }>;
  searchParams: SearchParams;
}

export interface AppRouteUrbanAreaPageParams {
  params: Params<{ urbanArea: string }>;
  searchParams: SearchParams;
}

export interface AppRouteTypesTypePageParams {
  params: Params<{ type: string }>;
  searchParams: SearchParams;
}

export interface AppRouteTypesTypeReviewsPageParams {
  params: Params<{ type: string }>;
  searchParams: SearchParams;
}

export interface AppRouteCategoriesCategoryPageParams {
  params: Params<{ category: string }>;
  searchParams: SearchParams;
}

export interface AppRouteCategoriesCategoryReviewsPageParams {
  params: Params<{ category: string }>;
  searchParams: SearchParams;
}

export interface AppRouteBrandTirePageParams {
  params: Params<{ brand: string }>;
  searchParams: SearchParams;
}

export interface AppRouteBrandCategoryPageParams {
  params: Params<{ brand: string; category: string }>;
  searchParams: SearchParams;
}

export interface AppRouteBrandTypePageParams {
  params: Params<{ brand: string; type: string }>;
  searchParams: SearchParams;
}

export interface AppRouteBrandReviewsPageParams {
  params: Params<{ brand: string }>;
  searchParams: SearchParams;
}

export interface AppRoutePaidPDPPageParams {
  params: Params<{ brand: string; productLine: string }>;
  searchParams: SearchParams;
}

export interface AppRoutePLPAndPDPPageParams {
  params: Params<{ brand: string; productLine: string }>;
  searchParams: SearchParams;
}

export interface AppRouteNewPDPPageParams {
  params: Params<{ brand: string; itemId: string; productLine: string }>;
  searchParams: SearchParams;
}

export interface AppRouteBrandProductLineReviewsPageParams {
  params: Params<{ brand: string; productLine: string }>;
  searchParams: SearchParams;
}

export interface AppRouteBrandProductLineWriteAReviewPageParams {
  params: Params<{ brand: string; productLine: string }>;
  searchParams: SearchParams;
}

export interface AppRouteVehiclesMakePageParams {
  params: Params<{ make: string }>;
  searchParams: SearchParams;
}

export interface AppRouteVehiclesMakeModelPageParams {
  params: Params<{ make: string; model: string }>;
  searchParams: SearchParams;
}

export interface AppRouteVehiclesMakeModelYearPageParams {
  params: Params<{ make: string; model: string; year: string }>;
  searchParams: SearchParams;
}

export interface AppRouteTireSizesPageParams {
  params: Params<{ size: string }>;
  searchParams: SearchParams;
}

export interface AppRouteTireSizesBrandPageParams {
  params: Params<{ brand: string; size: string }>;
  searchParams: SearchParams;
}

export interface AppRouteTireSizesBrandCategoryPageParams {
  params: Params<{ brand: string; category: string; size: string }>;
  searchParams: SearchParams;
}
export interface AppRouteTireSizesBrandSubtypePageParams {
  params: Params<{ brand: string; size: string; subtype: string }>;
  searchParams: SearchParams;
}

export interface AppRouteTireSizesCategoryPageParams {
  params: Params<{ category: string; size: string }>;
  searchParams: SearchParams;
}

export interface AppRouteTireSizesSubtypePageParams {
  params: Params<{ size: string; subtype: string }>;
  searchParams: SearchParams;
}
export interface AppRouteAccountPageParams {
  searchParams: Params<{ code?: string; state?: string }>;
}

export interface AppRoutePartnerProgramPageParams {
  searchParams: Params<{ uid: string }>;
}

export interface AppRouteIDMeCallbackPageParams {
  searchParams: Params<{ code: string }>;
}

export interface AppRouteCheckoutPaymentsPageParams {
  searchParams: SearchParams;
}

export interface AppRouteCheckoutRetrieveQuotePageParams {
  params: Params<{ quoteId: string }>;
  searchParams: SearchParams;
}

export interface AppRouteCheckoutConfirmOrderPageParams {
  params: Params<{ orderId: string }>;
  searchParams: SearchParams;
}

export interface AppRouteCheckoutOrderConfirmationPageParams {
  params: Params<{ orderId: string }>;
  searchParams: SearchParams;
}

export interface AppRouteCheckoutAffirmConfirmationPageParams {
  searchParams: SearchParams;
}
